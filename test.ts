import { highlight } from '@kdt310722/logger'
import { sleep } from '@kdt310722/utils/promise'
import { Limiter } from './app/utils/limiter/limiter'

let count = 0

const limiter = new Limiter({
    maxRequestsPerSecond: 1,
})

limiter.on('limit', (until) => {
    console.log('Limit until:', highlight(until.toISOString()))
})

const fake = async () => limiter.schedule(async () => {
    console.log('Request start:', ++count, highlight(new Date().toISOString()))

    await sleep(1000)
})

for (let i = 0; i < 5; i++) {
    fake()
}
