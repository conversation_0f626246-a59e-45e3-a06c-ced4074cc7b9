import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import { type LimiterScheduleOptions, Strategy, type StrategyEvents } from './strategy'

export interface FixedWindowStrategyConfig {
    capacity: number
    window: number
}

export class FixedWindowStrategy extends Strategy<FixedWindowStrategyConfig> {
    protected readonly bottleneck: Bottleneck
    protected readonly creationTime: number

    protected lastEmittedWindow = -1

    public constructor(config: FixedWindowStrategyConfig, emitter: Emitter<StrategyEvents>) {
        super(config, emitter)

        this.bottleneck = this.createBottleneck(config)
        this.creationTime = Date.now()
    }

    public async schedule<T>(fn: () => Awaitable<T>, { weight = 1, priority = 5 }: LimiterScheduleOptions = {}) {
        return this.bottleneck.schedule({ weight, priority }, async () => fn())
    }

    protected createBottleneck(config: FixedWindowStrategyConfig) {
        const bottleneck = new Bottleneck({
            reservoir: config.capacity,
            reservoirRefreshInterval: config.window,
            reservoirRefreshAmount: config.capacity,
            minTime: 0,
        })

        bottleneck.on('error', (error) => this.emitter.emit('error', error))

        bottleneck.on('depleted', () => {
            const now = Date.now()
            const timeSinceCreation = now - this.creationTime
            const currentWindow = Math.floor(timeSinceCreation / config.window)

            if (currentWindow !== this.lastEmittedWindow) {
                const nextRefreshTime = this.creationTime + ((currentWindow + 1) * config.window)
                const nextWindowAt = new Date(nextRefreshTime)

                this.lastEmittedWindow = currentWindow
                this.emitter.emit('limit', nextWindowAt, {})
            }
        })

        return bottleneck
    }
}
